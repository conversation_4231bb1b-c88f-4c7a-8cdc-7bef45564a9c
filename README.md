# Flarum Header Advertisement Slideshow

A Flarum extension that adds a customizable advertisement slideshow to the forum header with support for multiple slides and transition effects.

## Features

- **Header Advertisement Slideshow**: Display rotating advertisements in the forum header
- **Multiple Slides Support**: Configure up to 30 advertisement slides
- **Customizable Transitions**: Adjustable transition timing and effects
- **Header Icon**: Optional header icon display for non-logged users
- **Mobile Responsive**: Optimized for mobile devices
- **Swiper Integration**: Uses Swiper.js for smooth carousel effects

## Installation

```bash
composer require wusong8899/flarum-header-advertisement
```

## Configuration

1. Enable the extension in your Flarum admin panel
2. Configure advertisement slides in the extension settings:
   - Set transition time between slides
   - Add image URLs and link destinations for each slide
   - Configure header icon URL
3. The slideshow will automatically appear on the tags page

## Requirements

- Flarum ^1.0

## License

MIT License
