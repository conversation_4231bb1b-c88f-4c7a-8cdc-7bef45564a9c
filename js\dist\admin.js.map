{"version": 3, "file": "admin.js", "sources": ["../src/common/config/constants.ts", "../src/admin/components/dynamic-slide-settings-component.tsx", "../src/admin/settings-generator.ts", "../src/admin/index.ts"], "sourcesContent": ["/**\n * Application constants for Header Advertisement extension\n */\n\n// Mobile detection constants\nexport const MO<PERSON>LE_DETECTION = {\n  USER_AGENT_SUBSTR_START: 0,\n  USER_AGENT_SUBSTR_LENGTH: 4,\n} as const;\n\n// Error handling constants\nexport const ERROR_HANDLING = {\n  MAX_ERROR_LOG_ENTRIES: 50,\n  DOM_READY_TIMEOUT: 5000,\n  SLIDE_NUMBER_MIN: 1,\n  SLIDE_NUMBER_MAX: 30,\n  TRANSITION_TIME_MIN: 1000,\n  TRANSITION_TIME_MAX: 30_000,\n  CONFIG_MAX_SLIDES_MIN: 1,\n  CONFIG_MAX_SLIDES_MAX: 50,\n} as const;\n\n// Admin component constants\nexport const ADMIN_CONSTANTS = {\n  SAVE_DEBOUNCE_DELAY: 500,\n  DEFAULT_MAX_SLIDES: 30,\n  EMPTY_SLIDES_COUNT: 0,\n} as const;\n\n// UI styling constants\nexport const UI_STYLES = {\n  HEADER_ICON_HEIGHT: 24,\n  HEADER_ICON_MARGIN_TOP: 8,\n} as const;\n\n// Mobile layout constants\nexport const MOBILE_LAYOUT = {\n  SCREEN_WIDTH_MULTIPLIER: 2,\n  SCREEN_WIDTH_OFFSET: 50,\n  CONTAINER_MARGIN_MULTIPLIER: 0.254,\n} as const;\n\n// Slideshow constants\nexport const SLIDESHOW_CONSTANTS = {\n  SLIDE_INCREMENT: 1,\n  INITIAL_SLIDE_INDEX: 1,\n  VALIDATION_ERRORS_EMPTY: 0,\n} as const;\n\n// Array and index constants\nexport const ARRAY_CONSTANTS = {\n  EMPTY_LENGTH: 0,\n  FIRST_INDEX: 0,\n  NOT_FOUND_INDEX: -1,\n  NEXT_ITEM_OFFSET: 1,\n  LAST_ITEM_OFFSET: -1,\n} as const;\n\n// Timing constants\nexport const TIMING = {\n  CHECK_INTERVAL: 10,\n  DATA_CHECK_INTERVAL: 100,\n  DEFAULT_TRANSITION_TIME: 5000,\n} as const;\n\n// DOM element constants\nexport const DOM_ELEMENTS = {\n  SWIPER_AD_CONTAINER_ID: 'swiperAdContainer',\n  HEADER_ICON_ID: 'wusong8899HeaderAdvIcon',\n} as const;\n\n// CSS class constants\nexport const CSS_CLASSES = {\n  SWIPER: 'swiper',\n  SWIPER_WRAPPER: 'swiper-wrapper',\n  SWIPER_SLIDE: 'swiper-slide',\n  SWIPER_BUTTON_NEXT: 'swiper-button-next',\n  SWIPER_BUTTON_PREV: 'swiper-button-prev',\n  SWIPER_PAGINATION: 'swiper-pagination',\n  AD_SWIPER: 'adSwiper',\n} as const;\n\n// CSS selector constants\nexport const CSS_SELECTORS = {\n  APP_NAVIGATION_BACK_CONTROL: '#app-navigation .App-backControl',\n  CONTENT_CONTAINER: '#content .container',\n  NAV_ITEMS: '.item-nav',\n  SWIPER_PAGINATION_EL: '.swiper-pagination',\n  SWIPER_BUTTON_NEXT_EL: '.swiper-button-next',\n  SWIPER_BUTTON_PREV_EL: '.swiper-button-prev',\n} as const;\n\n// Extension configuration constants\nexport const EXTENSION_CONFIG = {\n  ID: 'wusong8899-flarum-header-advertisement',\n  TRANSLATION_PREFIX: 'wusong8899-header-advertisement',\n  MAX_SLIDES: 30,\n  HEADER_ICON_URL: 'https://ex.cc/assets/files/date/test.png',\n} as const;\n", "import app from 'flarum/admin/app';\nimport Component from 'flarum/common/Component';\nimport Button from 'flarum/common/components/Button';\n// oxlint-disable-next-line id-length\nimport m from 'mithril';\nimport { ADMIN_CONSTANTS } from '../../common/config/constants';\nimport type {\n  DynamicSlideSettingsComponentAttrs,\n  SlideDataInternal,\n  FlarumVnode\n} from '../../common/config/types';\n\n// Constants\nconst INITIAL_SLIDE_ID = 1; // Initial slide ID\nconst MINIMUM_SLIDES = 1; // Minimum number of slides required\nconst SLIDE_NOT_FOUND = -1; // Return value when slide is not found\nconst SLIDE_INCREMENT = 1; // Increment value for slide operations\n\n/**\n * Dynamic component for managing advertisement slide settings with add/delete functionality\n */\nexport default class DynamicSlideSettingsComponent extends Component<DynamicSlideSettingsComponentAttrs> {\n  private slides: SlideDataInternal[] = [];\n  private loading = false;\n  private nextId = INITIAL_SLIDE_ID;\n  private timeouts: Record<string, NodeJS.Timeout> = {};\n\n  oninit(vnode: FlarumVnode): void {\n    super.oninit(vnode);\n    this.loadExistingSlides();\n  }\n\n  /**\n   * Load existing slides from settings\n   */\n  private loadExistingSlides(): void {\n    const { extensionId, maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES } = this.attrs;\n    const slides: SlideDataInternal[] = [];\n\n    // Load existing slides from settings\n    for (let slideIndex = INITIAL_SLIDE_ID; slideIndex <= maxSlides; slideIndex += SLIDE_INCREMENT) {\n      const linkKey = `${extensionId}.Link${slideIndex}`;\n      const imageKey = `${extensionId}.Image${slideIndex}`;\n      const link = app.data.settings[linkKey] || '';\n      const image = app.data.settings[imageKey] || '';\n\n      // Only include slides that have at least one field filled\n      if (link || image) {\n        slides.push({\n          id: slideIndex,\n          link,\n          image\n        });\n        this.nextId = Math.max(this.nextId, slideIndex + SLIDE_INCREMENT);\n      }\n    }\n\n    this.slides = slides;\n\n    // If no slides exist, add one empty slide to start with\n    if (slides.length === ADMIN_CONSTANTS.EMPTY_SLIDES_COUNT) {\n      this.addSlide();\n    }\n  }\n\n  /**\n   * Add a new slide\n   */\n  private addSlide(): void {\n    const newSlide: SlideDataInternal = {\n      id: this.nextId,\n      link: '',\n      image: ''\n    };\n\n    this.nextId += 1;\n    this.slides.push(newSlide);\n    m.redraw();\n  }\n\n  /**\n   * Remove a slide\n   */\n  private removeSlide(slideId: number): void {\n    const { extensionId } = this.attrs;\n    const slideIndex = this.slides.findIndex(slide => slide.id === slideId);\n    \n    if (slideIndex === SLIDE_NOT_FOUND) {\n      return;\n    }\n    \n    const slide = this.slides[slideIndex];\n    \n    // Remove from backend\n    this.saveSetting(`${extensionId}.Link${slide.id}`, '');\n    this.saveSetting(`${extensionId}.Image${slide.id}`, '');\n    \n    // Remove from local state\n    this.slides.splice(slideIndex, SLIDE_INCREMENT);\n\n    // Ensure at least one slide exists\n    if (this.slides.length === ADMIN_CONSTANTS.EMPTY_SLIDES_COUNT) {\n      this.addSlide();\n    }\n    \n    m.redraw();\n  }\n\n  /**\n   * Update slide data\n   */\n  private updateSlide(slideId: number, field: 'link' | 'image', value: string): void {\n    const { extensionId } = this.attrs;\n    const slide = this.slides.find(slideItem => slideItem.id === slideId);\n\n    if (!slide) {\n      return;\n    }\n\n    slide[field] = value;\n\n    // Save to backend\n    let settingKey = '';\n    if (field === 'link') {\n      settingKey = `${extensionId}.Link${slide.id}`;\n    } else {\n      settingKey = `${extensionId}.Image${slide.id}`;\n    }\n\n    this.saveSetting(settingKey, value);\n  }\n\n  /**\n   * Save setting to backend with debouncing\n   */\n  private saveSetting(key: string, value: string): void {\n    // Clear existing timeout for this key\n    const timeoutKey = `saveTimeout_${key}`;\n    clearTimeout(this.timeouts[timeoutKey]);\n\n    // Set new timeout\n    this.timeouts[timeoutKey] = setTimeout(() => {\n      app.data.settings[key] = value;\n\n      app.request({\n        method: 'POST',\n        url: app.forum.attribute('apiUrl') + '/settings',\n        body: {\n          [key]: value\n        }\n      }).catch(() => {\n        // Handle save error silently for now\n      });\n    }, ADMIN_CONSTANTS.SAVE_DEBOUNCE_DELAY);\n  }\n\n  view(): unknown {\n    return m('div.Form-group', [\n      m('label.FormLabel',\n        app.translator.trans('wusong8899-header-advertisement.admin.SlideSettings')\n      ),\n      m('div.helpText',\n        app.translator.trans('wusong8899-header-advertisement.admin.SlideSettingsHelp')\n      ),\n\n      m('div.DynamicSlideSettings', [\n        // Slides list\n        this.slides.map((slide, slideIndex) => this.renderSlide(slide, slideIndex)),\n\n        // Add button\n        m('div.DynamicSlideSettings-addButton', [\n          m(Button, {\n            className: 'Button Button--primary',\n            icon: 'fas fa-plus',\n            onclick: () => this.addSlide()\n          }, app.translator.trans('wusong8899-header-advertisement.admin.AddSlide'))\n        ])\n      ])\n    ]);\n  }\n\n  /**\n   * Render a single slide\n   */\n  private renderSlide(slide: SlideDataInternal, slideIndex: number): unknown {\n    return m('div.DynamicSlideSettings-slide', {\n      key: slide.id\n    }, [\n      m('div.DynamicSlideSettings-slideHeader', [\n        m('h4', app.translator.trans('wusong8899-header-advertisement.admin.SlideNumber', { number: slideIndex + SLIDE_INCREMENT })),\n        m(Button, {\n          className: 'Button Button--danger',\n          icon: 'fas fa-trash',\n          onclick: () => this.removeSlide(slide.id),\n          disabled: this.slides.length === MINIMUM_SLIDES\n        }, app.translator.trans('wusong8899-header-advertisement.admin.DeleteSlide'))\n      ]),\n\n      m('div.DynamicSlideSettings-slideFields', [\n        // Link URL field\n        m('div.Form-group', [\n          m('label.FormLabel',\n            app.translator.trans('wusong8899-header-advertisement.admin.SlideLink')\n          ),\n          m('input.FormControl', {\n            type: 'url',\n            placeholder: 'https://example.com',\n            value: slide.link,\n            oninput: (event: Event) => {\n              const target = event.target as HTMLInputElement;\n              this.updateSlide(slide.id, 'link', target.value);\n            }\n          })\n        ]),\n\n        // Image URL field\n        m('div.Form-group', [\n          m('label.FormLabel',\n            app.translator.trans('wusong8899-header-advertisement.admin.SlideImage')\n          ),\n          m('input.FormControl', {\n            type: 'url',\n            placeholder: 'https://example.com/image.jpg',\n            value: slide.image,\n            oninput: (event: Event) => {\n              const target = event.target as HTMLInputElement;\n              this.updateSlide(slide.id, 'image', target.value);\n            }\n          })\n        ])\n      ])\n    ]);\n  }\n}\n", "import app from 'flarum/admin/app';\nimport DynamicSlideSettingsComponent from './components/dynamic-slide-settings-component';\n// oxlint-disable-next-line id-length\nimport m from 'mithril';\nimport { ADMIN_CONSTANTS } from '../common/config/constants';\nimport type { ExtensionData } from '../common/config/types';\n\n/**\n * Settings generator utility for Header Advertisement admin interface\n */\nexport class SettingsGenerator {\n    private extensionId: string;\n    private extensionData: ExtensionData;\n\n    constructor(extensionId: string) {\n        this.extensionId = extensionId;\n        this.extensionData = app.extensionData.for(extensionId) as ExtensionData;\n    }\n\n    /**\n     * Register transition time setting\n     */\n    registerTransitionTimeSetting(): this {\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.TransitionTime`,\n            type: 'number',\n            label: String(app.translator.trans('wusong8899-header-advertisement.admin.TransitionTime')),\n        });\n        return this;\n    }\n\n    /**\n     * Register header icon URL setting\n     */\n    registerHeaderIconUrlSetting(): this {\n        this.extensionData.registerSetting({\n            setting: `${this.extensionId}.HeaderIconUrl`,\n            type: 'url',\n            label: String(app.translator.trans('wusong8899-header-advertisement.admin.HeaderIconUrl')),\n            help: String(app.translator.trans('wusong8899-header-advertisement.admin.HeaderIconUrlHelp')),\n        });\n        return this;\n    }\n\n    /**\n     * Register dynamic slide settings component\n     * @param maxSlides - Maximum number of slides to configure\n     */\n    registerSlideSettings(maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES): this {\n        this.extensionData.registerSetting(() =>\n            m(DynamicSlideSettingsComponent, {\n                extensionId: this.extensionId,\n                maxSlides: maxSlides\n            })\n        );\n        return this;\n    }\n\n    /**\n     * Register all settings for the extension\n     * @param maxSlides - Maximum number of slides to configure\n     */\n    registerAllSettings(maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES): this {\n        return this\n            .registerTransitionTimeSetting()\n            .registerHeaderIconUrlSetting()\n            .registerSlideSettings(maxSlides);\n    }\n}\n\n/**\n * Configuration constants\n */\nexport const EXTENSION_CONFIG = {\n    EXTENSION_ID: 'wusong8899-flarum-header-advertisement',\n    MAX_SLIDES: ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES,\n    DEFAULT_TRANSITION_TIME: 5000,\n};\n\n/**\n * Initialize admin settings\n * @param extensionId - The extension identifier\n * @param maxSlides - Maximum number of slides\n */\nexport const initializeAdminSettings = (\n    extensionId = EXTENSION_CONFIG.EXTENSION_ID,\n    maxSlides = EXTENSION_CONFIG.MAX_SLIDES\n): void => {\n    const generator = new SettingsGenerator(extensionId);\n    generator.registerAllSettings(maxSlides);\n};\n", "import app from 'flarum/admin/app';\nimport { initializeAdminSettings } from './settings-generator';\n\napp.initializers.add('wusong8899-flarum-header-advertisement', (): void => {\n    initializeAdminSettings();\n});\n"], "names": ["ADMIN_CONSTANTS", "INITIAL_SLIDE_ID", "MINIMUM_SLIDES", "SLIDE_NOT_FOUND", "SLIDE_INCREMENT", "DynamicSlideSettingsComponent", "Component", "vnode", "extensionId", "maxSlides", "slides", "slideIndex", "linkKey", "image<PERSON>ey", "link", "app", "image", "newSlide", "m", "slideId", "slide", "field", "value", "slideItem", "<PERSON><PERSON><PERSON>", "key", "timeout<PERSON><PERSON>", "<PERSON><PERSON>", "event", "target", "SettingsGenerator", "EXTENSION_CONFIG", "initializeAdminSettings"], "mappings": "gCAuBO,MAAMA,EAAkB,CAC7B,oBAAqB,IACrB,mBAAoB,GACpB,mBAAoB,CACtB,ECdMC,EAAmB,EACnBC,EAAiB,EACjBC,EAAkB,GAClBC,EAAkB,EAKxB,MAAqBC,UAAsCC,CAA8C,CAAzG,aAAA,CAAA,MAAA,GAAA,SAAA,EACE,KAAQ,OAA8B,CAAA,EACtC,KAAQ,QAAU,GAClB,KAAQ,OAASL,EACjB,KAAQ,SAA2C,CAAA,CAAC,CAEpD,OAAOM,EAA0B,CAC/B,MAAM,OAAOA,CAAK,EAClB,KAAK,mBAAA,CACP,CAKQ,oBAA2B,CACjC,KAAM,CAAE,YAAAC,EAAa,UAAAC,EAAYT,EAAgB,kBAAA,EAAuB,KAAK,MACvEU,EAA8B,CAAA,EAGpC,QAASC,EAAaV,EAAkBU,GAAcF,EAAWE,GAAcP,EAAiB,CAC9F,MAAMQ,EAAU,GAAGJ,CAAW,QAAQG,CAAU,GAC1CE,EAAW,GAAGL,CAAW,SAASG,CAAU,GAC5CG,EAAOC,EAAI,KAAK,SAASH,CAAO,GAAK,GACrCI,EAAQD,EAAI,KAAK,SAASF,CAAQ,GAAK,IAGzCC,GAAQE,KACVN,EAAO,KAAK,CACV,GAAIC,EACJ,KAAAG,EACA,MAAAE,CAAA,CACD,EACD,KAAK,OAAS,KAAK,IAAI,KAAK,OAAQL,EAAaP,CAAe,EAEpE,CAEA,KAAK,OAASM,EAGVA,EAAO,SAAWV,EAAgB,oBACpC,KAAK,SAAA,CAET,CAKQ,UAAiB,CACvB,MAAMiB,EAA8B,CAClC,GAAI,KAAK,OACT,KAAM,GACN,MAAO,EAAA,EAGT,KAAK,QAAU,EACf,KAAK,OAAO,KAAKA,CAAQ,EACzBC,EAAE,OAAA,CACJ,CAKQ,YAAYC,EAAuB,CACzC,KAAM,CAAE,YAAAX,GAAgB,KAAK,MACvBG,EAAa,KAAK,OAAO,UAAUS,GAASA,EAAM,KAAOD,CAAO,EAEtE,GAAIR,IAAeR,EACjB,OAGF,MAAMiB,EAAQ,KAAK,OAAOT,CAAU,EAGpC,KAAK,YAAY,GAAGH,CAAW,QAAQY,EAAM,EAAE,GAAI,EAAE,EACrD,KAAK,YAAY,GAAGZ,CAAW,SAASY,EAAM,EAAE,GAAI,EAAE,EAGtD,KAAK,OAAO,OAAOT,EAAYP,CAAe,EAG1C,KAAK,OAAO,SAAWJ,EAAgB,oBACzC,KAAK,SAAA,EAGPkB,EAAE,OAAA,CACJ,CAKQ,YAAYC,EAAiBE,EAAyBC,EAAqB,CACjF,KAAM,CAAE,YAAAd,GAAgB,KAAK,MACvBY,EAAQ,KAAK,OAAO,KAAKG,GAAaA,EAAU,KAAOJ,CAAO,EAEpE,GAAI,CAACC,EACH,OAGFA,EAAMC,CAAK,EAAIC,EAGf,IAAIE,EAAa,GACbH,IAAU,OACZG,EAAa,GAAGhB,CAAW,QAAQY,EAAM,EAAE,GAE3CI,EAAa,GAAGhB,CAAW,SAASY,EAAM,EAAE,GAG9C,KAAK,YAAYI,EAAYF,CAAK,CACpC,CAKQ,YAAYG,EAAaH,EAAqB,CAEpD,MAAMI,EAAa,eAAeD,CAAG,GACrC,aAAa,KAAK,SAASC,CAAU,CAAC,EAGtC,KAAK,SAASA,CAAU,EAAI,WAAW,IAAM,CAC3CX,EAAI,KAAK,SAASU,CAAG,EAAIH,EAEzBP,EAAI,QAAQ,CACV,OAAQ,OACR,IAAKA,EAAI,MAAM,UAAU,QAAQ,EAAI,YACrC,KAAM,CACJ,CAACU,CAAG,EAAGH,CAAA,CACT,CACD,EAAE,MAAM,IAAM,CAEf,CAAC,CACH,EAAGtB,EAAgB,mBAAmB,CACxC,CAEA,MAAgB,CACd,OAAOkB,EAAE,iBAAkB,CACzBA,EAAE,kBACAH,EAAI,WAAW,MAAM,qDAAqD,CAAA,EAE5EG,EAAE,eACAH,EAAI,WAAW,MAAM,yDAAyD,CAAA,EAGhFG,EAAE,2BAA4B,CAE5B,KAAK,OAAO,IAAI,CAACE,EAAOT,IAAe,KAAK,YAAYS,EAAOT,CAAU,CAAC,EAG1EO,EAAE,qCAAsC,CACtCA,EAAES,EAAQ,CACR,UAAW,yBACX,KAAM,cACN,QAAS,IAAM,KAAK,SAAA,CAAS,EAC5BZ,EAAI,WAAW,MAAM,gDAAgD,CAAC,CAAA,CAC1E,CAAA,CACF,CAAA,CACF,CACH,CAKQ,YAAYK,EAA0BT,EAA6B,CACzE,OAAOO,EAAE,iCAAkC,CACzC,IAAKE,EAAM,EAAA,EACV,CACDF,EAAE,uCAAwC,CACxCA,EAAE,KAAMH,EAAI,WAAW,MAAM,oDAAqD,CAAE,OAAQJ,EAAaP,CAAA,CAAiB,CAAC,EAC3Hc,EAAES,EAAQ,CACR,UAAW,wBACX,KAAM,eACN,QAAS,IAAM,KAAK,YAAYP,EAAM,EAAE,EACxC,SAAU,KAAK,OAAO,SAAWlB,CAAA,EAChCa,EAAI,WAAW,MAAM,mDAAmD,CAAC,CAAA,CAC7E,EAEDG,EAAE,uCAAwC,CAExCA,EAAE,iBAAkB,CAClBA,EAAE,kBACAH,EAAI,WAAW,MAAM,iDAAiD,CAAA,EAExEG,EAAE,oBAAqB,CACrB,KAAM,MACN,YAAa,sBACb,MAAOE,EAAM,KACb,QAAUQ,GAAiB,CACzB,MAAMC,EAASD,EAAM,OACrB,KAAK,YAAYR,EAAM,GAAI,OAAQS,EAAO,KAAK,CACjD,CAAA,CACD,CAAA,CACF,EAGDX,EAAE,iBAAkB,CAClBA,EAAE,kBACAH,EAAI,WAAW,MAAM,kDAAkD,CAAA,EAEzEG,EAAE,oBAAqB,CACrB,KAAM,MACN,YAAa,gCACb,MAAOE,EAAM,MACb,QAAUQ,GAAiB,CACzB,MAAMC,EAASD,EAAM,OACrB,KAAK,YAAYR,EAAM,GAAI,QAASS,EAAO,KAAK,CAClD,CAAA,CACD,CAAA,CACF,CAAA,CACF,CAAA,CACF,CACH,CACF,CC/NO,MAAMC,CAAkB,CAI3B,YAAYtB,EAAqB,CAC7B,KAAK,YAAcA,EACnB,KAAK,cAAgBO,EAAI,cAAc,IAAIP,CAAW,CAC1D,CAKA,+BAAsC,CAClC,YAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,kBAC5B,KAAM,SACN,MAAO,OAAOO,EAAI,WAAW,MAAM,sDAAsD,CAAC,CAAA,CAC7F,EACM,IACX,CAKA,8BAAqC,CACjC,YAAK,cAAc,gBAAgB,CAC/B,QAAS,GAAG,KAAK,WAAW,iBAC5B,KAAM,MACN,MAAO,OAAOA,EAAI,WAAW,MAAM,qDAAqD,CAAC,EACzF,KAAM,OAAOA,EAAI,WAAW,MAAM,yDAAyD,CAAC,CAAA,CAC/F,EACM,IACX,CAMA,sBAAsBN,EAAYT,EAAgB,mBAA0B,CACxE,YAAK,cAAc,gBAAgB,IAC/BkB,EAAEb,EAA+B,CAC7B,YAAa,KAAK,YAClB,UAAAI,CAAA,CACH,CAAA,EAEE,IACX,CAMA,oBAAoBA,EAAYT,EAAgB,mBAA0B,CACtE,OAAO,KACF,8BAAA,EACA,6BAAA,EACA,sBAAsBS,CAAS,CACxC,CACJ,CAKO,MAAMsB,EAAmB,CAC5B,aAAc,yCACd,WAAY/B,EAAgB,kBAEhC,EAOagC,EAA0B,CACnCxB,EAAcuB,EAAiB,aAC/BtB,EAAYsB,EAAiB,aACtB,CACW,IAAID,EAAkBtB,CAAW,EACzC,oBAAoBC,CAAS,CAC3C,ECvFAM,EAAI,aAAa,IAAI,yCAA0C,IAAY,CACvEiB,EAAA,CACJ,CAAC"}